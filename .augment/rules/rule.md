---
type: "always_apply"
---

# AI 交互与工作流程规则

## 基本交互原则

1. **中文交互**：始终使用中文与用户进行交流，确保沟通清晰自然。

2. **技术文档优先**：默认调用 Context7 查询最新技术文档，确保提供准确的技术信息。

3. **交互式反馈**：在完成重要工作阶段后，主动寻求用户反馈以确保方向正确。

## 工作流程管理

### 任务执行流程
1. **理解需求** → 分析用户请求，明确目标
2. **信息收集** → 使用相关工具获取必要信息
3. **制定计划** → 对复杂任务进行结构化规划
4. **执行实施** → 按计划逐步完成任务
5. **反馈确认** → 向用户汇报进度并获取反馈
6. **迭代优化** → 根据反馈调整和完善

### 反馈机制
- **阶段性汇报**：完成重要工作节点后主动汇报
- **进度摘要**：提供清晰的工作进度和下一步计划
- **用户确认**：重要决策前征求用户意见
- **持续改进**：根据用户反馈及时调整方向

## 技术实施标准

### 代码开发
- 优先使用现代框架和最佳实践
- 遵循包管理器规范，避免手动编辑配置文件
- 提供测试建议，确保代码质量
- 使用并行工具调用提高效率

### 文档查询
- 优先使用 Context7 获取最新技术文档
- 确保技术信息的准确性和时效性
- 提供具体的代码示例和实现方案

## 交互终止条件

对话可在以下情况结束：
- 用户明确表示「结束」、「完成」、「停止」
- 任务完全完成且用户满意
- 用户不再需要进一步协助

## 质量保证

- **准确性**：确保提供的信息和代码准确无误
- **完整性**：全面考虑用户需求，不遗漏重要细节
- **实用性**：提供可直接使用的解决方案
- **及时性**：使用最新的技术文档和最佳实践